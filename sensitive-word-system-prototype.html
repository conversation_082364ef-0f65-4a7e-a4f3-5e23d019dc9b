<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>敏感词过滤系统原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .prototype-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .prototype-title {
            text-align: center;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 40px;
            color: #2c3e50;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin: 40px 0 20px 0;
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .page-mockup {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .page-mockup:hover {
            transform: translateY(-5px);
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
        }

        .page-content {
            padding: 20px;
            min-height: 500px;
        }

        /* 管理后台样式 */
        .admin-layout {
            display: flex;
            min-height: 500px;
        }

        .admin-sidebar {
            width: 200px;
            min-width: 200px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            flex-shrink: 0;
        }

        .admin-sidebar .logo {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .admin-menu {
            list-style: none;
        }

        .admin-menu li {
            margin-bottom: 5px;
        }

        .admin-menu a {
            display: block;
            color: #bdc3c7;
            text-decoration: none;
            padding: 12px 20px;
            transition: all 0.3s;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background: #34495e;
            color: white;
        }

        .admin-main {
            flex: 1;
            min-width: 0;
            background: #ecf0f1;
            padding: 20px;
        }

        .admin-header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .admin-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
            margin-right: 8px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        /* 标签样式 */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-high {
            background: #e74c3c;
            color: white;
        }

        .badge-medium {
            background: #f39c12;
            color: white;
        }

        .badge-low {
            background: #27ae60;
            color: white;
        }

        /* 搜索栏 */
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }

        .search-bar input {
            flex: 1;
            max-width: 300px;
        }

        /* Tab 样式 */
        .tabs {
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .tab-nav {
            display: flex;
            list-style: none;
        }

        .tab-nav li {
            margin-right: 20px;
        }

        .tab-nav a {
            display: block;
            padding: 12px 0;
            text-decoration: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-nav a.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }

        /* 小程序样式 */
        .miniapp-mockup {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .miniapp-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .miniapp-content {
            padding: 20px;
        }

        .comment-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .comment-input {
            width: 100%;
            min-height: 80px;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            resize: vertical;
        }

        .comment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .char-count {
            color: #666;
            font-size: 12px;
        }

        /* 图标样式 */
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            margin-right: 5px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination a {
            padding: 8px 12px;
            margin: 0 2px;
            text-decoration: none;
            border: 1px solid #ddd;
            color: #666;
            border-radius: 4px;
        }

        .pagination a.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 动画效果 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <h1 class="prototype-title">敏感词过滤系统 - 完整原型设计</h1>
        
        <!-- 管理后台部分 -->
        <h2 class="section-title">管理后台页面</h2>
        
        <div class="pages-grid">
            <!-- 1. 管理后台 - 敏感词管理页面 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 敏感词管理</div>
                <div class="page-content">
                    <div class="admin-layout">
                        <div class="admin-sidebar">
                            <div class="logo">敏感词系统</div>
                            <ul class="admin-menu">
                                <li><a href="#" class="active">敏感词管理</a></li>
                                <li><a href="#">评论管理</a></li>
                                <li><a href="#">系统设置</a></li>
                            </ul>
                        </div>
                        <div class="admin-main">
                            <div class="admin-header">
                                <h3>敏感词管理</h3>
                            </div>
                            <div class="admin-content">
                                <div class="search-bar">
                                    <input type="text" class="form-control" placeholder="搜索敏感词...">
                                    <select class="form-control" style="width: auto;">
                                        <option>全部等级</option>
                                        <option>高危</option>
                                        <option>中危</option>
                                        <option>低危</option>
                                    </select>
                                    <button class="btn btn-primary">搜索</button>
                                    <button class="btn btn-success">添加敏感词</button>
                                    <button class="btn btn-warning">批量导入</button>
                                </div>
                                
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>敏感词</th>
                                            <th>等级</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>暴力</td>
                                            <td><span class="badge badge-high">高危</span></td>
                                            <td>2024-01-15 10:30</td>
                                            <td>
                                                <button class="btn btn-primary">编辑</button>
                                                <button class="btn btn-danger">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>垃圾</td>
                                            <td><span class="badge badge-medium">中危</span></td>
                                            <td>2024-01-15 09:20</td>
                                            <td>
                                                <button class="btn btn-primary">编辑</button>
                                                <button class="btn btn-danger">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>笨蛋</td>
                                            <td><span class="badge badge-low">低危</span></td>
                                            <td>2024-01-14 16:45</td>
                                            <td>
                                                <button class="btn btn-primary">编辑</button>
                                                <button class="btn btn-danger">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <div class="pagination">
                                    <a href="#">上一页</a>
                                    <a href="#" class="active">1</a>
                                    <a href="#">2</a>
                                    <a href="#">3</a>
                                    <a href="#">下一页</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. 管理后台 - 评论管理页面 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 评论管理</div>
                <div class="page-content">
                    <div class="admin-layout">
                        <div class="admin-sidebar">
                            <div class="logo">敏感词系统</div>
                            <ul class="admin-menu">
                                <li><a href="#">敏感词管理</a></li>
                                <li><a href="#" class="active">评论管理</a></li>
                                <li><a href="#">系统设置</a></li>
                            </ul>
                        </div>
                        <div class="admin-main">
                            <div class="admin-header">
                                <h3>评论管理</h3>
                            </div>
                            <div class="admin-content">
                                <div class="tabs">
                                    <ul class="tab-nav">
                                        <li><a href="#" class="active">评论列表</a></li>
                                        <li><a href="#">敏感词过滤</a></li>
                                    </ul>
                                </div>

                                <div class="search-bar">
                                    <input type="text" class="form-control" placeholder="搜索评论内容...">
                                    <select class="form-control" style="width: auto;">
                                        <option>全部状态</option>
                                        <option>正常</option>
                                        <option>已过滤</option>
                                        <option>已删除</option>
                                    </select>
                                    <button class="btn btn-primary">搜索</button>
                                </div>

                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>评论内容</th>
                                            <th>评论人</th>
                                            <th>归属内容</th>
                                            <th>评论时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>这个视频真的很***，完全是垃圾内容</td>
                                            <td>用户001</td>
                                            <td>视频：如何学习编程</td>
                                            <td>2024-01-15 14:30</td>
                                            <td><span class="badge badge-warning">已过滤</span></td>
                                            <td>
                                                <button class="btn btn-primary">查看详情</button>
                                                <button class="btn btn-danger">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>很好的教程，学到了很多</td>
                                            <td>用户002</td>
                                            <td>图文：前端开发指南</td>
                                            <td>2024-01-15 13:20</td>
                                            <td><span class="badge badge-low">正常</span></td>
                                            <td>
                                                <button class="btn btn-primary">查看详情</button>
                                                <button class="btn btn-danger">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>作者是个***，内容完全没用</td>
                                            <td>用户003</td>
                                            <td>视频：JavaScript基础</td>
                                            <td>2024-01-15 12:15</td>
                                            <td><span class="badge badge-high">已删除</span></td>
                                            <td>
                                                <button class="btn btn-primary">查看详情</button>
                                                <button class="btn btn-success">恢复</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div class="pagination">
                                    <a href="#">上一页</a>
                                    <a href="#" class="active">1</a>
                                    <a href="#">2</a>
                                    <a href="#">3</a>
                                    <a href="#">下一页</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. 管理后台 - 敏感词过滤详情页面 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 敏感词过滤详情</div>
                <div class="page-content">
                    <div class="admin-layout">
                        <div class="admin-sidebar">
                            <div class="logo">敏感词系统</div>
                            <ul class="admin-menu">
                                <li><a href="#">敏感词管理</a></li>
                                <li><a href="#" class="active">评论管理</a></li>
                                <li><a href="#">系统设置</a></li>
                            </ul>
                        </div>
                        <div class="admin-main">
                            <div class="admin-header">
                                <h3>敏感词过滤详情</h3>
                            </div>
                            <div class="admin-content">
                                <div class="tabs">
                                    <ul class="tab-nav">
                                        <li><a href="#">评论列表</a></li>
                                        <li><a href="#" class="active">敏感词过滤</a></li>
                                    </ul>
                                </div>

                                <div class="search-bar">
                                    <input type="text" class="form-control" placeholder="搜索过滤记录...">
                                    <select class="form-control" style="width: auto;">
                                        <option>全部等级</option>
                                        <option>高危</option>
                                        <option>中危</option>
                                        <option>低危</option>
                                    </select>
                                    <button class="btn btn-primary">搜索</button>
                                </div>

                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>原始内容</th>
                                            <th>过滤后内容</th>
                                            <th>触发敏感词</th>
                                            <th>等级</th>
                                            <th>过滤时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>这个视频真的很垃圾，完全没用</td>
                                            <td>这个视频真的很***，完全没用</td>
                                            <td>垃圾</td>
                                            <td><span class="badge badge-medium">中危</span></td>
                                            <td>2024-01-15 14:30</td>
                                            <td>
                                                <button class="btn btn-primary">查看详情</button>
                                                <button class="btn btn-success">通过</button>
                                                <button class="btn btn-danger">拒绝</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>作者是个笨蛋，内容完全没用</td>
                                            <td>作者是个***，内容完全没用</td>
                                            <td>笨蛋</td>
                                            <td><span class="badge badge-low">低危</span></td>
                                            <td>2024-01-15 12:15</td>
                                            <td>
                                                <button class="btn btn-primary">查看详情</button>
                                                <button class="btn btn-success">通过</button>
                                                <button class="btn btn-danger">拒绝</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div class="pagination">
                                    <a href="#">上一页</a>
                                    <a href="#" class="active">1</a>
                                    <a href="#">2</a>
                                    <a href="#">下一页</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 小程序前端部分 -->
        <h2 class="section-title">小程序前端页面</h2>

        <div class="pages-grid">
            <!-- 4. 小程序 - 评论发布页面 -->
            <div class="page-mockup">
                <div class="page-header">小程序 - 评论发布页面</div>
                <div class="page-content">
                    <div class="miniapp-mockup">
                        <div class="miniapp-header">
                            <h3>发表评论</h3>
                        </div>
                        <div class="miniapp-content">
                            <div class="comment-box">
                                <h4>视频：如何学习前端开发</h4>
                                <p style="color: #666; font-size: 14px; margin-top: 5px;">发布者：技术分享者</p>
                            </div>

                            <div class="form-group">
                                <label class="form-label">写下你的评论</label>
                                <textarea class="comment-input" placeholder="请输入你的评论内容...">这个教程真的很垃圾，完全没用</textarea>
                            </div>

                            <div class="comment-actions">
                                <span class="char-count">25/200</span>
                                <button class="btn btn-primary">发布评论</button>
                            </div>

                            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px;">
                                <h5 style="color: #495057; margin-bottom: 10px;">💡 温馨提示</h5>
                                <p style="color: #6c757d; font-size: 14px;">• 请文明发言，系统会自动过滤不当内容</p>
                                <p style="color: #6c757d; font-size: 14px;">• 评论发布后如包含敏感词会自动替换为"***"</p>
                                <p style="color: #6c757d; font-size: 14px;">• 严重违规内容可能被直接删除</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 5. 小程序 - 评论成功页面 -->
            <div class="page-mockup">
                <div class="page-header">小程序 - 评论发布成功</div>
                <div class="page-content">
                    <div class="miniapp-mockup">
                        <div class="miniapp-header">
                            <h3>评论发布成功</h3>
                        </div>
                        <div class="miniapp-content">
                            <div style="text-align: center; padding: 40px 20px;">
                                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" style="margin-bottom: 20px;">
                                    <circle cx="12" cy="12" r="10" fill="#27ae60"/>
                                    <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <h4 style="color: #27ae60; margin-bottom: 10px;">评论发布成功！</h4>
                                <p style="color: #666; font-size: 14px; margin-bottom: 30px;">你的评论已经发布，感谢你的参与</p>

                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                                    <h5 style="margin-bottom: 10px;">发布的评论：</h5>
                                    <p style="color: #666; font-size: 14px;">"这个教程真的很***，完全没用"</p>
                                    <small style="color: #999; font-size: 12px;">系统已自动过滤不当内容</small>
                                </div>

                                <button class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">查看更多评论</button>
                                <button class="btn" style="width: 100%; background: #f8f9fa; color: #666;">返回视频</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 6. 小程序 - 评论发布中页面 -->
            <div class="page-mockup">
                <div class="page-header">小程序 - 评论发布中</div>
                <div class="page-content">
                    <div class="miniapp-mockup">
                        <div class="miniapp-header">
                            <h3>发布评论</h3>
                        </div>
                        <div class="miniapp-content">
                            <div style="text-align: center; padding: 60px 20px;">
                                <div style="width: 60px; height: 60px; margin: 0 auto 20px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                                <h4 style="color: #333; margin-bottom: 10px;">正在发布评论...</h4>
                                <p style="color: #666; font-size: 14px; margin-bottom: 20px;">系统正在检测内容并发布</p>

                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                                    <h5 style="margin-bottom: 10px;">你的评论：</h5>
                                    <p style="color: #666; font-size: 14px;">"这个教程真的很垃圾，完全没用"</p>
                                </div>

                                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: left;">
                                    <h5 style="color: #1976d2; margin-bottom: 10px;">🔍 内容检测中</h5>
                                    <p style="color: #1976d2; font-size: 14px;">正在进行敏感词检测和内容审核...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 7. 小程序 - 评论列表页面 -->
            <div class="page-mockup">
                <div class="page-header">小程序 - 评论列表</div>
                <div class="page-content">
                    <div class="miniapp-mockup">
                        <div class="miniapp-header">
                            <h3>评论列表</h3>
                        </div>
                        <div class="miniapp-content">
                            <div class="comment-box">
                                <h4>视频：如何学习前端开发</h4>
                                <p style="color: #666; font-size: 14px; margin-top: 5px;">共 23 条评论</p>
                            </div>

                            <!-- 评论项 1 -->
                            <div style="border-bottom: 1px solid #eee; padding: 15px 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 32px; height: 32px; background: #3498db; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; margin-right: 10px;">A</div>
                                    <div>
                                        <div style="font-weight: 500; font-size: 14px;">用户001</div>
                                        <div style="color: #999; font-size: 12px;">2小时前</div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 14px; line-height: 1.5;">这个教程真的很***，完全没用</p>
                                <div style="margin-top: 10px;">
                                    <button style="background: none; border: none; color: #666; font-size: 12px; margin-right: 15px;">👍 12</button>
                                    <button style="background: none; border: none; color: #666; font-size: 12px;">💬 回复</button>
                                </div>
                            </div>

                            <!-- 评论项 2 -->
                            <div style="border-bottom: 1px solid #eee; padding: 15px 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 32px; height: 32px; background: #e74c3c; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; margin-right: 10px;">B</div>
                                    <div>
                                        <div style="font-weight: 500; font-size: 14px;">用户002</div>
                                        <div style="color: #999; font-size: 12px;">3小时前</div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 14px; line-height: 1.5;">很好的教程，学到了很多东西，感谢分享！</p>
                                <div style="margin-top: 10px;">
                                    <button style="background: none; border: none; color: #666; font-size: 12px; margin-right: 15px;">👍 25</button>
                                    <button style="background: none; border: none; color: #666; font-size: 12px;">💬 回复</button>
                                </div>
                            </div>

                            <!-- 评论项 3 -->
                            <div style="border-bottom: 1px solid #eee; padding: 15px 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 32px; height: 32px; background: #f39c12; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; margin-right: 10px;">C</div>
                                    <div>
                                        <div style="font-weight: 500; font-size: 14px;">用户003</div>
                                        <div style="color: #999; font-size: 12px;">5小时前</div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 14px; line-height: 1.5;">请问有没有更高级的教程推荐？</p>
                                <div style="margin-top: 10px;">
                                    <button style="background: none; border: none; color: #666; font-size: 12px; margin-right: 15px;">👍 8</button>
                                    <button style="background: none; border: none; color: #666; font-size: 12px;">💬 回复</button>
                                </div>
                            </div>

                            <div style="text-align: center; padding: 20px;">
                                <button class="btn btn-primary" style="width: 100%;">发表评论</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理后台弹窗页面 -->
        <h2 class="section-title">管理后台弹窗页面</h2>

        <div class="pages-grid">
            <!-- 8. 添加敏感词弹窗 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 添加敏感词弹窗</div>
                <div class="page-content">
                    <div style="background: rgba(0,0,0,0.5); padding: 50px; display: flex; align-items: center; justify-content: center; min-height: 400px;">
                        <div style="background: white; border-radius: 8px; padding: 30px; width: 100%; max-width: 500px;">
                            <h3 style="margin-bottom: 20px;">添加敏感词</h3>

                            <div class="form-group">
                                <label class="form-label">敏感词内容</label>
                                <input type="text" class="form-control" placeholder="请输入敏感词">
                            </div>

                            <div class="form-group">
                                <label class="form-label">敏感词等级</label>
                                <select class="form-control">
                                    <option>请选择等级</option>
                                    <option>高危</option>
                                    <option>中危</option>
                                    <option>低危</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">替换字符</label>
                                <input type="text" class="form-control" value="***" placeholder="替换字符">
                            </div>

                            <div class="form-group">
                                <label class="form-label">备注说明</label>
                                <textarea class="form-control" rows="3" placeholder="可选，添加备注说明"></textarea>
                            </div>

                            <div style="text-align: right; margin-top: 20px;">
                                <button class="btn" style="background: #f8f9fa; color: #666; margin-right: 10px;">取消</button>
                                <button class="btn btn-primary">确认添加</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 9. 批量导入敏感词弹窗 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 批量导入敏感词</div>
                <div class="page-content">
                    <div style="background: rgba(0,0,0,0.5); padding: 50px; display: flex; align-items: center; justify-content: center; min-height: 400px;">
                        <div style="background: white; border-radius: 8px; padding: 30px; width: 100%; max-width: 600px;">
                            <h3 style="margin-bottom: 20px;">批量导入敏感词</h3>

                            <div class="form-group">
                                <label class="form-label">选择文件</label>
                                <input type="file" class="form-control" accept=".txt,.csv,.xlsx">
                                <small style="color: #666; font-size: 12px;">支持 .txt、.csv、.xlsx 格式文件</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">默认等级</label>
                                <select class="form-control">
                                    <option>中危</option>
                                    <option>高危</option>
                                    <option>低危</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">文件格式说明</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-size: 14px;">
                                    <p style="margin-bottom: 5px;">• TXT格式：每行一个敏感词</p>
                                    <p style="margin-bottom: 5px;">• CSV格式：敏感词,等级,备注</p>
                                    <p style="margin-bottom: 0;">• Excel格式：第一列敏感词，第二列等级，第三列备注</p>
                                </div>
                            </div>

                            <div style="text-align: right; margin-top: 20px;">
                                <button class="btn" style="background: #f8f9fa; color: #666; margin-right: 10px;">取消</button>
                                <button class="btn btn-warning">开始导入</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 10. 评论详情弹窗 -->
            <div class="page-mockup">
                <div class="page-header">管理后台 - 评论详情</div>
                <div class="page-content">
                    <div style="background: rgba(0,0,0,0.5); padding: 50px; display: flex; align-items: center; justify-content: center; min-height: 400px;">
                        <div style="background: white; border-radius: 8px; padding: 30px; width: 100%; max-width: 700px;">
                            <h3 style="margin-bottom: 20px;">评论详情</h3>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                <div>
                                    <label class="form-label">评论人</label>
                                    <p style="color: #333;">用户001</p>
                                </div>
                                <div>
                                    <label class="form-label">评论时间</label>
                                    <p style="color: #333;">2024-01-15 14:30:25</p>
                                </div>
                                <div>
                                    <label class="form-label">归属内容</label>
                                    <p style="color: #333;">视频：如何学习编程</p>
                                </div>
                                <div>
                                    <label class="form-label">当前状态</label>
                                    <p><span class="badge badge-warning">已过滤</span></p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">原始评论内容</label>
                                <div style="background: #fff3cd; padding: 15px; border-radius: 6px; border: 1px solid #ffeaa7;">
                                    这个视频真的很垃圾，完全是垃圾内容
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">过滤后内容</label>
                                <div style="background: #d1ecf1; padding: 15px; border-radius: 6px; border: 1px solid #bee5eb;">
                                    这个视频真的很***，完全是***内容
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">触发的敏感词</label>
                                <div style="display: flex; gap: 10px;">
                                    <span class="badge badge-medium">垃圾 (中危)</span>
                                </div>
                            </div>

                            <div style="text-align: right; margin-top: 20px;">
                                <button class="btn" style="background: #f8f9fa; color: #666; margin-right: 10px;">关闭</button>
                                <button class="btn btn-success" style="margin-right: 10px;">通过审核</button>
                                <button class="btn btn-danger">删除评论</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统特性说明 -->
        <h2 class="section-title">系统核心特性</h2>
        <div style="background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 40px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div>
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🚀 高效过滤算法</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>AC自动机算法，时间复杂度O(n)</li>
                        <li>支持多模式匹配</li>
                        <li>Redis缓存提升性能</li>
                        <li>实时敏感词检测</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 智能管理系统</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>敏感词等级分类管理</li>
                        <li>批量导入导出功能</li>
                        <li>评论审核工作流</li>
                        <li>详细的过滤日志</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🎯 用户体验优化</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>友好的提示信息</li>
                        <li>自动替换敏感词</li>
                        <li>实时字数统计</li>
                        <li>流畅的交互体验</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔧 系统扩展性</h4>
                    <ul style="color: #666; line-height: 1.8;">
                        <li>支持自定义替换字符</li>
                        <li>可配置敏感词等级</li>
                        <li>灵活的过滤规则</li>
                        <li>易于集成和部署</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; padding: 40px; color: #666;">
            <p style="font-size: 16px; margin-bottom: 10px;">敏感词过滤系统 - 完整原型设计</p>
            <p style="font-size: 14px;">包含管理后台和小程序前端的完整功能原型</p>
        </div>
    </div>
</body>
</html>
